import argparse, os, sys, shutil, subprocess, glob, tempfile, platform, time
from pathlib import Path

import torch
from PIL import Image
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModelForCausalLM
from transformers import __version__ as transformers_version
from packaging import version

# Check for a recent version of transformers
if version.parse(transformers_version) < version.parse("4.43.0"):
    print(f"Warning: Your transformers version is {transformers_version}. "
          f"This script works best with transformers>=4.43.0. "
          f"Please consider upgrading: pip install git+https://github.com/huggingface/transformers.git",
          file=sys.stderr)

# Optional: Whisper for audio
try:
    import whisper
    HAS_WHISPER = True
except Exception:
    HAS_WHISPER = False

def run_ffmpeg_extract_frames(video_path, out_dir, fps=1, width=1280):
    os.makedirs(out_dir, exist_ok=True)
    # Scale keeps aspect ratio; qscale 2 is good quality JPG
    cmd = [
        "ffmpeg", "-i", video_path,
        "-vf", f"fps={fps},scale={width}:-1",
        "-qscale:v", "2",
        os.path.join(out_dir, "f_%05d.jpg"),
        "-hide_banner", "-loglevel", "error"
    ]
    subprocess.run(cmd, check=True)

def transcribe_audio(video_path, model_size="small", device="auto"):
    if not HAS_WHISPER:
        print("Whisper not installed. Skipping audio transcription.", file=sys.stderr)
        return None
    if device == "auto":
        device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Transcribing audio with Whisper ({model_size}) on {device}...")
    asr = whisper.load_model(model_size, device=device)
    result = asr.transcribe(video_path, fp16=(device=="cuda"))
    return result.get("text", "").strip()

def load_qwen_vl(model_id, quantize=None):
    tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
    kwargs = dict(trust_remote_code=True, device_map="auto")
    # Choose dtype
    if torch.cuda.is_available():
        kwargs["torch_dtype"] = torch.float16
    elif torch.backends.mps.is_available():
        kwargs["torch_dtype"] = torch.float16
    else:
        kwargs["torch_dtype"] = torch.float32

    # Optional 4-bit quantization (requires bitsandbytes on Linux/NVIDIA)
    if quantize in ("4bit", "4"):
        try:
            kwargs["load_in_4bit"] = True
        except Exception as e:
            print("4-bit quantization requested but not available. Falling back to non-quantized.", file=sys.stderr)

    model = AutoModelForCausalLM.from_pretrained(model_id, **kwargs)
    return tokenizer, model

def main():
    parser = argparse.ArgumentParser(description="Local video description with Qwen2.5-VL")
    parser.add_argument("--video", required=True, help="Path to input video (e.g., input.mp4)")
    parser.add_argument("--fps", type=float, default=1.0, help="Frames per second to sample")
    parser.add_argument("--max_frames", type=int, default=32, help="Max frames to pass to the model")
    parser.add_argument("--width", type=int, default=1280, help="Resize frame width (keeps aspect)")
    parser.add_argument("--use_audio", action="store_true", help="Transcribe audio with Whisper and include it")
    parser.add_argument("--whisper_model", default="small", help="Whisper size: tiny/base/small/medium/large")
    parser.add_argument("--model_id", default="Qwen/Qwen2-VL-7B", help="VLM model id") #Qwen3-VL-4B #Qwen2.5-VL-7B-Instruct
    parser.add_argument("--quantize", default=None, choices=[None, "4bit", "4"], help="Use 4-bit quantization (Linux+CUDA)")
    parser.add_argument("--out", default=None, help="Optional path to save the description (.txt)")
    parser.add_argument("--prompt", default=None, help="Custom instruction prompt")
    args = parser.parse_args()

    video_path = Path(args.video)
    assert video_path.exists(), f"Video not found: {video_path}"

    # Extract frames
    tmpdir = tempfile.mkdtemp(prefix="vidframes_")
    try:
        print("Extracting frames with ffmpeg...")
        run_ffmpeg_extract_frames(str(video_path), tmpdir, fps=args.fps, width=args.width)
        frame_paths = sorted(glob.glob(os.path.join(tmpdir, "*.jpg")))
        if not frame_paths:
            raise RuntimeError("No frames extracted. Is ffmpeg installed and video readable?")

        # Subsample to max_frames evenly
        if len(frame_paths) > args.max_frames:
            step = max(1, len(frame_paths) // args.max_frames)
            frame_paths = frame_paths[::step][:args.max_frames]

        print(f"Using {len(frame_paths)} frames.")

        # Optional audio transcription
        transcript = None
        if args.use_audio:
            transcript = transcribe_audio(str(video_path), model_size=args.whisper_model)

        # Load model
        print("Loading Qwen2.5-VL model (this may take a minute on first run)...")
        tokenizer, model = load_qwen_vl(args.model_id, quantize=args.quantize)

        # Build the multi-modal input
        images = [{"image": p} for p in frame_paths]
        if args.prompt:
            prompt = args.prompt
        else:
            prompt = (
                "You are a helpful video description assistant. "
                "Describe the video in 4–6 sentences. Summarize the setting, main actions, "
                "people/objects, and notable changes over time. "
                "If possible, mention approximate timing like early/middle/late."
            )
        items = images + [{"text": prompt}]
        if transcript:
            items.append({"text": f"Audio transcript (use as context, do not quote verbatim): {transcript}"})

        query = tokenizer.from_list_format(items)

        # Generate
        print("Generating description...")
        t0 = time.time()
        response, _ = model.chat(tokenizer, query=query, history=None)
        dt = time.time() - t0

        print("\n=== Video description ===\n")
        print(response)
        print(f"\n(Generated in {dt:.1f} s)")

        if args.out:
            with open(args.out, "w", encoding="utf-8") as f:
                f.write(response.strip() + "\n")
            print(f"Saved to: {args.out}")

    finally:
        # Clean up frames
        shutil.rmtree(tmpdir, ignore_errors=True)

if __name__ == "__main__":
    main()